package me.zivush.smp.plugins;

import me.zivush.smp.SMP;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;

import java.util.*;

public class SMPPluginManager {
    private final SMP plugin;
    private final List<SMPPlugin> plugins = new ArrayList<>();

    public SMPPluginManager(SMP plugin) {
        this.plugin = plugin;
    }

    public void enablePlugin(String smpName, String pluginName, boolean everyone, Player owner, boolean sendMessage) {
        ConfigurationSection pluginsConfig = plugin.getConfig().getConfigurationSection("plugins");
        if (pluginsConfig == null || !pluginsConfig.contains(pluginName)) {
            plugin.sendMessage(owner, "plugin_not_found");
            return;
        }

        int maxPlugins = plugin.getMaxPermissionValue(owner, "smp.maxplugins.", 3);
        List<String> permissions = pluginsConfig.getStringList(pluginName + ".permissions");
        List<String> commands = pluginsConfig.getStringList(pluginName + ".commands");
        String[] worldTypes = {"", "_nether", "_the_end"};

        // Save enabled plugin to database
        List<String> enabledPlugins = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins");
        if (enabledPlugins.size() >= maxPlugins) {
            plugin.sendMessage(owner, "max_plugins_reached");
            return;
        }
        if (!enabledPlugins.contains(pluginName)) {
            enabledPlugins.add(pluginName);
            plugin.getDatabase().set("smps." + smpName + ".enabled_plugins", enabledPlugins);
        }
        plugin.getDatabase().set("smps." + smpName + ".plugin_settings." + pluginName + ".everyone", everyone);

        if (everyone) {
            // Get all players from any world in the SMP
            Set<Player> smpPlayers = new HashSet<>();
            for (String type : worldTypes) {
                String worldName = plugin.generateWorldName(smpName, type);
                World world = Bukkit.getWorld(worldName);
                if (world != null) {
                    smpPlayers.addAll(world.getPlayers());
                }
            }

            // Apply permissions for all worlds to each player
            for (Player player : smpPlayers) {
                for (String type : worldTypes) {
                    String worldName = plugin.generateWorldName(smpName, type);
                    for (String permission : permissions) {
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(),
                                "lp user " + player.getName() + " permission set " + permission + " true world=" + worldName);
                    }
                }
            }
        } else {
            // Apply permissions for all worlds to owner only
            for (String type : worldTypes) {
                String worldName = plugin.generateWorldName(smpName, type);
                for (String permission : permissions) {
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(),
                            "lp user " + owner.getName() + " permission set " + permission + " true world=" + worldName);
                }
            }
        }

        // Notify about plugin commands if available
        if (sendMessage) {
            plugin.sendMessage(owner, "plugin_enabled", "%plugin%", pluginName);
            if (!commands.isEmpty()) {
                plugin.sendMessage(owner, "plugin_commands_enabled", "%plugin%", pluginName);
            }
        }
    }


    public void disablePlugin(String smpName, String pluginName, Player owner, boolean sendMessage) {
        ConfigurationSection pluginsConfig = plugin.getConfig().getConfigurationSection("plugins");
        if (pluginsConfig == null || !pluginsConfig.contains(pluginName)) {
            plugin.sendMessage(owner, "plugin_not_found");
            return;
        }

        List<String> permissions = pluginsConfig.getStringList(pluginName  + ".permissions");
        String[] worldTypes = {"", "_nether", "_the_end"};

        // Remove from enabled plugins
        List<String> enabledPlugins = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins");
        enabledPlugins.remove(pluginName);
        plugin.getDatabase().set("smps." + smpName + ".enabled_plugins", enabledPlugins);
        plugin.getDatabase().set("smps." + smpName + ".plugin_settings." + pluginName, null);

        // Remove permissions
        for (String type : worldTypes) {
            String worldName = plugin.generateWorldName(smpName, type);
            for (String permission : permissions) {
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(),
                        "lp bulkupdate all delete \"world == " + worldName.toLowerCase() + "\" \"permission == " + permission + "\"");
            }
        }
        if (sendMessage) plugin.sendMessage(owner, "plugin_disabled", "%plugin%", pluginName);
    }

    public void handlePlayerJoin(Player player, String smpName) {
        List<String> enabledPlugins = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins");
        ConfigurationSection pluginsConfig = plugin.getConfig().getConfigurationSection("plugins");
        String[] worldTypes = {"", "_nether", "_the_end"};

        for (String pluginName : enabledPlugins) {
            boolean everyone = plugin.getDatabase().getBoolean("smps." + smpName + ".plugin_settings." + pluginName + ".everyone");
            if (everyone) {
                List<String> permissions = pluginsConfig.getStringList(pluginName  + ".permissions");
                for (String type : worldTypes) {
                    String worldName = plugin.generateWorldName(smpName, type);
                    for (String permission : permissions) {
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(),
                                "lp user " + player.getName() + " permission set " + permission + " true world=" + worldName.toLowerCase());
                    }
                }
            }
        }
    }

    public void cleanupSMP(String smpName) {
        String[] worldTypes = {"", "_nether", "_the_end"};
        for (String type : worldTypes) {
            String worldName = plugin.generateWorldName(smpName, type);
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(),
                    "lp bulkupdate all delete \"world == " + worldName.toLowerCase() + "\"");
        }
    }
    public void loadPlugins() {
        ConfigurationSection pluginsConfig = plugin.getConfig().getConfigurationSection("plugins");
        if(pluginsConfig == null) return;

        for(String pluginId : pluginsConfig.getKeys(false)) {
            ConfigurationSection iconSection = pluginsConfig.getConfigurationSection(pluginId + ".icon");
            List<String> dependencies = pluginsConfig.getStringList(pluginId + ".dependencies");
            boolean allowAccessSelection = pluginsConfig.getBoolean(pluginId + ".allow_access_selection", true);

            plugins.add(new SMPPlugin(
                    pluginId,
                    pluginsConfig.getStringList(pluginId + ".permissions"),
                    pluginsConfig.getStringList(pluginId + ".commands"),
                    dependencies,
                    allowAccessSelection,
                    new SMPPlugin.PluginIcon(
                            iconSection.getString("material"),
                            iconSection.getString("name"),
                            iconSection.getStringList("lore"),
                            iconSection.getInt("custom_model_data")
                    )
            ));
        }
    }

    public List<SMPPlugin> getPlugins() {
        return plugins;
    }
}
