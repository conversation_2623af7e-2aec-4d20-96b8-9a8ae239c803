package me.zivush.smp.gui;

import me.zivush.smp.SMP;
import me.zivush.smp.plugins.SMPPlugin;
import net.wesjd.anvilgui.AnvilGUI;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.stream.Collectors;

public class SMPPluginGUI {
    private final SMP plugin;
    private final Map<UUID, Integer> playerPages = new HashMap<>();
    private final Map<UUID, String> playerFilters = new HashMap<>(); // <-- Added this line
    private final SMPPluginAccessGUI accessGUI;

    public SMPPluginGUI(SMP plugin) {
        this.plugin = plugin;
        this.accessGUI = new SMPPluginAccessGUI(plugin);
    }

    public void openGUI(Player player, String smpName, int page) {
        openGUI(player, smpName, page, "");
    }

    public void openGUI(Player player, String smpName) {
        openGUI(player, smpName, 0, "");
    }

    public void openGUI(Player player, String smpName, int page, String filter) {
        ConfigurationSection guiConfig = plugin.getConfig().getConfigurationSection("gui.smp_plugins");
        Inventory inv = Bukkit.createInventory(null, guiConfig.getInt("size"),
                plugin.colorize(guiConfig.getString("title")));

        // Set filler items
        ItemStack filler = createItem(guiConfig.getConfigurationSection("filler"));
        for(int i = 0; i < inv.getSize(); i++) {
            inv.setItem(i, filler);
        }

        // Get filtered plugins
        List<SMPPlugin> plugins = plugin.getPluginManager().getPlugins().stream()
                .filter(p -> p.getId().toLowerCase().contains(filter.toLowerCase())) // <-- Changed startsWith to contains
                .collect(Collectors.toList());

        List<Integer> slots = guiConfig.getIntegerList("plugins.slots");
        int maxPerPage = slots.size();
        int totalPages = (plugins.size() - 1) / maxPerPage + 1;

        playerPages.put(player.getUniqueId(), page);
        playerFilters.put(player.getUniqueId(), filter);

        // Add filtered plugins for current page
        int start = page * maxPerPage;
        for(int i = 0; i < maxPerPage && start + i < plugins.size(); i++) {
            SMPPlugin smpPlugin = plugins.get(start + i);
            inv.setItem(slots.get(i), createPluginItem(smpPlugin, guiConfig, smpName));
        }

        // Add navigation buttons
        inv.setItem(guiConfig.getInt("next_page.slot"),
                createItem(guiConfig.getConfigurationSection("next_page")));
        inv.setItem(guiConfig.getInt("previous_page.slot"),
                createItem(guiConfig.getConfigurationSection("previous_page")));
        inv.setItem(guiConfig.getInt("search.slot"),
                createItem(guiConfig.getConfigurationSection("search")));
        inv.setItem(guiConfig.getInt("close.slot"),
                createItem(guiConfig.getConfigurationSection("close")));

        player.openInventory(inv);
    }

    private ItemStack createPluginItem(SMPPlugin smpPlugin, ConfigurationSection guiConfig, String smpName) {
        ItemStack item = new ItemStack(Material.valueOf(smpPlugin.getIcon().getMaterial()));
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName(plugin.colorize(smpPlugin.getIcon().getName()));

        List<String> lore = new ArrayList<>(smpPlugin.getIcon().getLore());

        // Add dependency information
        if (!smpPlugin.getDependencies().isEmpty()) {
            lore.add("");
            lore.add("&7Dependencies:");
            for (String dependency : smpPlugin.getDependencies()) {
                boolean depEnabled = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins")
                        .contains(dependency);
                String status = depEnabled ? "&a✓" : "&c✗";
                lore.add("  " + status + " &f" + dependency);
            }
        }

        // Add access selection info
        if (!smpPlugin.isAllowAccessSelection()) {
            lore.add("");
            lore.add("&cOwner only - no access selection");
        }

        // Add enabled/disabled status
        boolean isEnabled = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins")
                .contains(smpPlugin.getId());

        if(isEnabled) {
            meta.addEnchant(org.bukkit.enchantments.Enchantment.DURABILITY, 1, true);
            meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
        }

        // Add status-specific lore
        lore.add("");
        if(isEnabled) {
            if(smpPlugin.isAllowAccessSelection()) {
                lore.addAll(guiConfig.getStringList("plugins.enabled_lore"));
            } else {
                lore.add("&aPlugin is enabled (Owner only)");
                lore.add("&7Left Click to disable");
            }
        } else {
            boolean canEnable = plugin.getPluginManager().areDependenciesMet(smpName, smpPlugin.getId());
            if(!canEnable) {
                lore.addAll(guiConfig.getStringList("plugins.dependency_missing_lore"));
            } else if(smpPlugin.isAllowAccessSelection()) {
                lore.addAll(guiConfig.getStringList("plugins.disabled_lore"));
            } else {
                lore.add("&cPlugin is disabled");
                lore.add("&7Click to enable (Owner only)");
            }
        }

        meta.setLore(lore.stream().map(plugin::colorize).collect(Collectors.toList()));

        if(smpPlugin.getIcon().getCustomModelData() > 0) {
            meta.setCustomModelData(smpPlugin.getIcon().getCustomModelData());
        }

        item.setItemMeta(meta);
        return item;
    }

    private ItemStack createItem(ConfigurationSection section) {
        String materialName = section.getString("material");
        Material material;
        if (materialName == null) {
            plugin.getLogger().warning("Missing material in config section: " + section.getCurrentPath());
            material = Material.STONE; // Default fallback
        } else {
            material = Material.valueOf(materialName);
        }

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        String name = section.getString("name", " "); // Default to empty space if missing
        meta.setDisplayName(plugin.colorize(name));

        if(section.contains("lore")) {
            meta.setLore(section.getStringList("lore").stream()
                    .map(plugin::colorize)
                    .collect(Collectors.toList()));
        }

        if(section.getInt("custom_model_data") > 0) {
            meta.setCustomModelData(section.getInt("custom_model_data"));
        }

        item.setItemMeta(meta);
        return item;
    }

    public void handleClick(Player player, int slot, String smpName, boolean isRightClick) {
        ConfigurationSection guiConfig = plugin.getConfig().getConfigurationSection("gui.smp_plugins");
        int page = playerPages.getOrDefault(player.getUniqueId(), 0);

        if(slot == guiConfig.getInt("close.slot")) {
            player.closeInventory();
            playerFilters.remove(player.getUniqueId());
            playerPages.remove(player.getUniqueId());
        }
        else if(slot == guiConfig.getInt("search.slot")) {
            openSearchAnvil(player, smpName);
        }
        else if(slot == guiConfig.getInt("next_page.slot")) {
            int totalPlugins = plugin.getPluginManager().getPlugins().size();
            int maxPerPage = guiConfig.getIntegerList("plugins.slots").size();
            if((page + 1) * maxPerPage < totalPlugins) {
                openGUI(player, smpName, page + 1);
            }
        }
        else if(slot == guiConfig.getInt("previous_page.slot")) {
            if(page > 0) {
                openGUI(player, smpName, page - 1);
            }
        }
        else {
            List<Integer> slots = guiConfig.getIntegerList("plugins.slots");
            if(slots.contains(slot)) {
                int index = slots.indexOf(slot) + (page * slots.size());
                List<SMPPlugin> plugins = plugin.getPluginManager().getPlugins();
                if(index < plugins.size()) {
                    SMPPlugin clickedPlugin = plugins.get(index);
                    boolean isEnabled = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins")
                            .contains(clickedPlugin.getId());

                    if(isRightClick && isEnabled && clickedPlugin.isAllowAccessSelection()) {
                        accessGUI.openGUI(player, smpName, clickedPlugin.getId());
                    } else if(!isRightClick) {
                        togglePlugin(player, smpName, clickedPlugin.getId(), page);
                    }
                }
            }
        }
    }


    private void togglePlugin(Player player, String smpName, String pluginId, int page) {
        List<String> enabledPlugins = plugin.getDatabase().getStringList("smps." + smpName + ".enabled_plugins");
        SMPPlugin smpPlugin = plugin.getPluginManager().getPluginById(pluginId);

        if(enabledPlugins.contains(pluginId)) {
            plugin.getPluginManager().disablePlugin(smpName, pluginId, player, true);
            openGUI(player, smpName, page);
        } else {
            if(smpPlugin != null && smpPlugin.isAllowAccessSelection()) {
                player.closeInventory();
                accessGUI.openGUI(player, smpName, pluginId);
            } else {
                // Enable plugin for owner only (no access selection)
                plugin.getPluginManager().enablePlugin(smpName, pluginId, false, player, true);
                openGUI(player, smpName, page);
            }
        }
    }

    private void openSearchAnvil(Player player, String smpName) {
        new AnvilGUI.Builder()
                .onClick((slot, stateSnapshot) -> {
                    String search = stateSnapshot.getText().toLowerCase();
                    openGUI(player, smpName, 0, search);

                    return Arrays.asList(AnvilGUI.ResponseAction.close());
                })
                .text(plugin.getConfig().getString("gui.smp_plugins.search.text"))
                .title(plugin.colorize(plugin.getConfig().getString("gui.smp_plugins.search.title")))
                .plugin(plugin)
                .open(player);
    }
    public SMPPluginAccessGUI getAccessGUI() {
        return accessGUI;
    }
}
